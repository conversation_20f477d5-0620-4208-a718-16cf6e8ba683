import { FC, useEffect, useRef, useState } from 'react';
import './style.css';
import { IMAGES } from '../../constant/image';

type LoadingIndexes = '1' | '2' | '3' | '4' | '5';

const SPINNER_TICK_TIME = 500;

export const GameLoadingScreen: FC<{ isOuter?: boolean }> = ({ isOuter }) => {
  const spinnerInterval = useRef<number>(0);
  const bgIndex = useRef<string>(String(Math.ceil((Math.random() / 2) * 10)));

  const [spinnerIndex, setSpinnerIndex] = useState<string>('1');

  useEffect(() => {
    spinnerInterval.current = window.setInterval(() => {
      setSpinnerIndex((prevIndex) => {
        const convPrevIndex = Number(prevIndex);
        return String(convPrevIndex === 5 ? 1 : convPrevIndex + 1);
      });
    }, SPINNER_TICK_TIME);

    return () => clearInterval(spinnerInterval.current);
  }, []);

  return (
    <div
      className={`game-loading-screen touch-none ${
        isOuter ? 'absolute z-50 top-0 left-0 right-0 bottom-0 max-w-md' : ''
      }`}
      style={{
        backgroundImage: `url(${
          IMAGES[`LOADING_BG_${bgIndex.current as LoadingIndexes}`]
        })`,
        ...(isOuter && { margin: '0 auto' }),
      }}
    >
      <img
        className="spinner"
        src={IMAGES[`SPINNER_${spinnerIndex as LoadingIndexes}`]}
      />
    </div>
  );
};
