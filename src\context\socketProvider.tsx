import { createContext, useContext, useEffect, useState } from "react";
import { SocketURL, SOCKET_URL_FOR_LEVEL } from "../api/auth";
import useWebSocket from "../hooks/useSocket";

type SocketContextType = {
  socket: WebSocket | null;
  levelPlayerSocket: WebSocket | null;

  socketState: any;
  levelPlayerState: any;

  setSocketState: (data: any) => void;
  setLevelPlayerState: (data: any) => void;

  connectSocket: () => void;
  connectLevelPlayerSocket: () => void;

  disconnectSocket: () => void;
  disconnectLevelPlayerSocket: () => void;

  isConnected: boolean;
  isLevelPlayerConnected: boolean;
};
const SocketContext = createContext<SocketContextType | undefined>(undefined);

export const SocketProvider = ({ children }: { children: React.ReactNode }) => {
  // Initialize with connectOnMount: false to prevent auto-connect at start
  const { socket, isConnected, connect, disconnect } = useWebSocket(SocketURL, {
    connectOnMount: false,
    autoReconnect: false,
  });

  const {
    socket: levelPlayerSocket,
    isConnected: isLevelPlayerConnected,
    connect: connectLevelPlayerSocket,
    disconnect: disconnectLevelPlayerSocket,
  } = useWebSocket(SOCKET_URL_FOR_LEVEL, { connectOnMount: false });

  const [socketState, setSocketState] = useState<any>(null);
  const [levelPlayerState, setLevelPlayerState] = useState<any>(null);
  useEffect(() => {
    if (!socket) return;

    const messageHandler = (event: MessageEvent) => {
      try {
        const parsed = event.data ? JSON.parse(event.data) : null;
        setSocketState(parsed);
      } catch (error) {
        console.error("Failed to parse socket message:", error);
      }
    };

    socket.addEventListener('message', messageHandler);

    return () => {
      socket.removeEventListener('message', messageHandler);
    };
  }, [socket]);

  useEffect(() => {
    if (!levelPlayerSocket) return;

    const handler = (event: MessageEvent) => {
      try {
        const data = JSON.parse(event.data);
        setLevelPlayerState(data);
      } catch (err) {
        console.error("Error parsing level player socket data:", err);
      }
    };

    levelPlayerSocket.addEventListener("message", handler);
    return () => levelPlayerSocket.removeEventListener("message", handler);
  }, [levelPlayerSocket]);

  return (
    <SocketContext.Provider
      value={{
        socket,
        levelPlayerSocket,
        levelPlayerState,
        setLevelPlayerState,
        connectLevelPlayerSocket,
        disconnectLevelPlayerSocket,
        isLevelPlayerConnected,
        socketState,
        setSocketState,
        connectSocket: connect,
        disconnectSocket: disconnect,
        isConnected
      }}
    >
      {children}
    </SocketContext.Provider>
  );
};

export const useSocketContext = () => {
  const context = useContext(SocketContext);
  if (!context) {
    throw new Error("useSocketContext must be used within a SocketProvider");
  }
  return context;
};