import axios from 'axios';
import { SEARCH_SQUAD, SQUAD_CREATE } from './auth';


type Squad = {
    created_at: string;
    handle: string;
    type: string;
};
type squadSearch={
    name:string
}



export const searchSquad = async (data: squadSearch, token: string) => {
    const response = await axios.get(SEARCH_SQUAD, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
      params: {
       name:data?.name,
      },
    });
  
    return response.data;
  };


export const createSquad = async (data: Squad, token: string) => {
    const response = await axios.post(SQUAD_CREATE, data, {
        headers: {
            Authorization: `Bearer ${token}`,
        },
    });
    return response.data;
};