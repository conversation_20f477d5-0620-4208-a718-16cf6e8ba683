import { Flip, toast } from "react-toastify";

export const CustomToast = (type: any, message: any) => {
    const toastOptions = {
        position: "top-right",
        autoClose: 1500,
        hideProgressBar: true,
        closeOnClick: true,
        pauseOnHover: false,
        draggable: true,
        progress: undefined,
        theme: "colored",
        transition: Flip,
        // Add custom style only for success type
        ...(type === 'success' && {
            style: {
                background: 'linear-gradient(to right, #FFD700, #FFA500)',
                color: '#000', // Optional: set text color
            }
        }),
    };

    return toast[type](message, toastOptions);
};