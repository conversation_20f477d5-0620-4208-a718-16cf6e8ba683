@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  background-color: #1a1b1e;
  color: #ffffff;
}

@keyframes fade-in {
  from {
    opacity: 0;
    transform: scale(0.9);
  }

  to {
    opacity: 1;
    transform: scale(1);
  }
}

.animate-fade-in {
  animation: fade-in 0.3s ease-out forwards;
}

@keyframes carnival-lights {

  0%,
  100% {
    opacity: 1;
    transform: scale(1);
  }

  50% {
    opacity: 0.5;
    transform: scale(0.8);
  }
}

.animate-carnival-lights {
  animation: carnival-lights 1s ease-in-out infinite;
}

@font-face {
  font-family: 'Haydes';
  src: url('./assets/font/Haydes.ttf') format('truetype');
  font-weight: 400;
  font-style: normal;
}

.outlined-text {
  fill: white;
  fill-opacity: 1;
  stroke: #000000;
  stroke-width: 1px;
  font-weight: 400;
}

input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus {
  transition: background-color 9999s ease-in-out 0s;
  -webkit-text-fill-color: #ffffff !important;
  caret-color: #ffffff;
  box-shadow: 0 0 0px 1000px rgba(255, 255, 255, 0.1) inset !important;
  /* your desired background */
}