// File: src/components/images.js
// (1) Import each image at the top. Replace these with your actual filenames:
import Plinko from '../assets/game_logo/plinko.png';
import QuickHustule from '../assets/game_logo/highlow.png';
import bitCoins from '../assets/game_logo/dice.png';
import CryptoKing from '../assets/game_logo/crypto.png';

// Card Images
import Card from '../assets/images/card_1.png';
import Card_2 from '../assets/images/card_2.png';
import Card_3 from '../assets/images/card_3.png';
import Card_4 from '../assets/images/card_4.png';
import Card_5 from '../assets/images/card_5.png';
import Card_6 from '../assets/images/card_6.png';
import Card_7 from '../assets/images/card_7.png';
import Card_8 from '../assets/images/card_8.png';
import Card_9 from '../assets/images/card_9.png';
import Card_10 from '../assets/images/card_10.png';
import Card_11 from '../assets/images/card_11.png';
import Card_12 from '../assets/images/card_12.png';
import Card_13 from '../assets/images/card_13.png';

// Loading Backgrounds
import loadingBg1 from '../assets/images/loading-bg-1.webp';
import loadingBg2 from '../assets/images/loading-bg-2.webp';
import loadingBg3 from '../assets/images/loading-bg-3.webp';
import loadingBg4 from '../assets/images/loading-bg-4.webp';
import loadingBg5 from '../assets/images/loading-bg-5.webp';

// Spinners
import spinner1 from '../assets/images/spinner-1.png';
import spinner2 from '../assets/images/spinner-2.png';
import spinner3 from '../assets/images/spinner-3.png';
import spinner4 from '../assets/images/spinner-4.png';
import spinner5 from '../assets/images/spinner-5.png';
import WatchIcon from '../assets/game_logo/Dimaond.svg'; // your “watch video” icon
import MoreWaysIcon from '../assets/game_logo/Dimaond1.svg';
import SpinnigWheel from '../assets/game_logo/Spinning_Wheel.png';
import ScratchCard from '../assets/game_logo/Scratch_Card.png';

// Scratch Icons and Background
import ScratchBg from '../assets/images/background.avif';
import cupImg from '../assets/images/Scratch-cap.svg';
import dollarImg from '../assets/images/Scratch-doller.svg';
import tokenImg from '../assets/images/Scratch-token.svg';
import artImg from '../assets/images/Scratch-art.svg'; // default
import lootbox from '../assets/game_logo/lootbox.png';
import scratchCardGame from '../assets/game_logo/scretch-card-game.png';

// Scratch Cards
import scratchCard1 from '../assets/images/scratch-card-1.png';
import scratchCard2 from '../assets/images/scratch-card-2.png';
import scratchCard3 from '../assets/images/scratch-card-3.png';
import scratchCard4 from '../assets/images/scratch-card-4.png';
import scratchCard5 from '../assets/images/scratch-card-5.png';
import scratchCard6 from '../assets/images/scratch-card-6.png';
import scratchCard7 from '../assets/images/scratch-card-7.png';
import scratchCard8 from '../assets/images/scratch-card-8.png';
import scratchCard9 from '../assets/images/scratch-card-9.png';

// Scratch Frames
import capFrames from '../assets/images/frames-cap.png';
import arTimeFrames from '../assets/images/frames-artime.png';
import dollarFrames from '../assets/images/frames-dollar.png';
import dollar100Frames from '../assets/images/frames-dollar-100.png';
import tokenFrames from '../assets/images/frames-token.png';
import token100Frames from '../assets/images/frames-token-100.png';

// Others
import eclipseBg from '../assets/images/eclipse-bg.png';

// Maps
import map_1 from '../assets/map/map_1.png';
import map_2 from '../assets/map/map_2.png';
import map_3 from '../assets/map/map_3.png';
import map_4 from '../assets/map/map_4.png';
import map_5 from '../assets/map/map_5.png';
import map_6 from '../assets/map/map_6.png';
import map_7 from '../assets/map/map_7.png';
import map_8 from '../assets/map/map_8.png';
import map_9 from '../assets/map/map_9.png';
import map_10 from '../assets/map/map_10.png';
import map_11 from '../assets/map/map_11.png';
import lock from '../assets/map/Lock.svg';

// Sellers Images
import capo from '../assets/sellers/CAPO.png';
import cannector from '../assets/sellers/CONNECTOR.png';
import corner_hustle from '../assets/sellers/CORNER_HUSTLER.png';
import kingpin from '../assets/sellers/KINGPIN.png';
import og from '../assets/sellers/OG.png';
import shot_caller from '../assets/sellers/SHOT_CALLER.png';
import shot_caller_2 from '../assets/sellers/SHOT_CALLER_2.png';
import shot_caller_3 from '../assets/sellers/SHOT_CALLER_3.png';
import street_boss from '../assets/sellers/STREET_BOSS.png';
import Street_scout from '../assets/sellers/STREET_SCOUT.png';
import underboss from '../assets/sellers/UNDERBOSS.png';

// Spinning wheel game
import light from '../assets/games/spinning_wheel/light.svg';
import wheelSpear from '../assets/games/spinning_wheel/wheel-spear.svg';
import segmentBorder from '../assets/games/spinning_wheel/segment-border.png';
import baseballCap from '../assets/games/spinning_wheel/baseball-cap.png';
import better from '../assets/games/spinning_wheel/diamond.svg';
import dollar from '../assets/games/spinning_wheel/dollar.svg';
import internetTraffic from '../assets/games/spinning_wheel/internet-traffic.svg';
import mystery from '../assets/games/spinning_wheel/mistery.avif';
import wheelBorders from '../assets/games/spinning_wheel/wheel-borders.svg';

// Plinko game
import plinkoFieldBg from '../assets/games/plinko/plinko-field-bg.avif';

// Squad Banner Image
import squad_image from '../assets/images/squad_banner.avif';
export const IMAGES = {
  // Game Logos
  PLINKO: Plinko,
  QUICK_HUSTULE: QuickHustule,
  BIT_COINS: bitCoins,
  CRYPTO_KING: CryptoKing,
  WATCH_ICON: WatchIcon,
  MOREWAYS_ICON: MoreWaysIcon,
  SPINNING_WHEEL: SpinnigWheel,
  SCRATCH_CARD: ScratchCard,
  SCRATCH_CARD_GAME: scratchCardGame,
  LOOTBOX: lootbox,

  // Cards
  CARD_1: Card,
  CARD_2: Card_2,
  CARD_3: Card_3,
  CARD_4: Card_4,
  CARD_5: Card_5,
  CARD_6: Card_6,
  CARD_7: Card_7,
  CARD_8: Card_8,
  CARD_9: Card_9,
  CARD_10: Card_10,
  CARD_11: Card_11,
  CARD_12: Card_12,
  CARD_13: Card_13,

  // Loading Backgrounds
  LOADING_BG_1: loadingBg1,
  LOADING_BG_2: loadingBg2,
  LOADING_BG_3: loadingBg3,
  LOADING_BG_4: loadingBg4,
  LOADING_BG_5: loadingBg5,

  // Spinners
  SPINNER_1: spinner1,
  SPINNER_2: spinner2,
  SPINNER_3: spinner3,
  SPINNER_4: spinner4,
  SPINNER_5: spinner5,

  // Scratch Background and Icons
  SCRATCH_BG: ScratchBg,
  SCRATCH_CUP: cupImg,
  SCRATCH_DOLLAR: dollarImg,
  SCRATCH_TOKEN: tokenImg,
  SCRATCH_ART: artImg,

  // Scratch Cards
  SCRATCH_CARD_1: scratchCard1,
  SCRATCH_CARD_2: scratchCard2,
  SCRATCH_CARD_3: scratchCard3,
  SCRATCH_CARD_4: scratchCard4,
  SCRATCH_CARD_5: scratchCard5,
  SCRATCH_CARD_6: scratchCard6,
  SCRATCH_CARD_7: scratchCard7,
  SCRATCH_CARD_8: scratchCard8,
  SCRATCH_CARD_9: scratchCard9,

  // Scratch Frames
  CAP_FRAMES: capFrames,
  ARTIME_FRAMES: arTimeFrames,
  DOLLAR_FRAMES: dollarFrames,
  DOLLAR_100_FRAMES: dollar100Frames,
  TOKEN_FRAMES: tokenFrames,
  TOKEN_100_FRAMES: token100Frames,

  // Others
  ECLIPSE_BG: eclipseBg,

  // Maps
  MAP_1: map_1,
  MAP_2: map_2,
  MAP_3: map_3,
  MAP_4: map_4,
  MAP_5: map_5,
  MAP_6: map_6,
  MAP_7: map_7,
  MAP_8: map_8,
  MAP_9: map_9,
  MAP_10: map_10,
  MAP_11: map_11,
  LOCK: lock,
  SHOT_CALLER_3: shot_caller_3,
  STREET_SCOUT: Street_scout,

  // seller images
  CAPO: capo,
  CONNECTOR: cannector,
  CORNER_HUSTLE: corner_hustle,
  KINGPIN: kingpin,
  OG: og,
  SHOT_CALLER: shot_caller,
  SHOTE_CELLER_2: shot_caller_2,
  STREET_BOSS: street_boss,
  UNDERBOSS: underboss,

  // Spinning wheel game
  LIGHT: light,
  WHEEL_SPEAR: wheelSpear,
  SEGMENT_BORDER: segmentBorder,
  BASEBALL_CAP: baseballCap,
  BETTER: better,
  DOLLAR: dollar,
  INTERNET_TRAFFIC: internetTraffic,
  MYSTERY: mystery,
  WHEEL_BORDERS: wheelBorders,

  // Plinko
  PLINKO_FIELD_BG: plinkoFieldBg,

  // squad images
  SQUAD_BANNER_IMAGE: squad_image,
};
