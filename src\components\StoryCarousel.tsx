import React from 'react';
import useEmblaCarousel from 'embla-carousel-react';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { IMAGES } from '../constant/image';
const STORY_SLIDES = [
  {
    title: "From Streets to Success",
    description:
      "Welcome to Playzuzu, where your journey from street hustler to Lagos legend begins. Every champion starts somewhere - your story starts here.",
    image: IMAGES.CARD_2,
  },
  {
    title: "Master the Hustle",
    description:
      "Start small, think big. Learn the streets, play smart, and build your empire one game at a time. In Lagos, opportunity knocks for those who dare to answer.",
    image: IMAGES.CARD_6,
  },
  {
    title: "Rise Through the Ranks",
    description:
      "From street corners to high-stakes games, your reputation grows with every win. Climb the ladder, earn respect, and become a true Lagos high-roller.",
    image: IMAGES.CARD_4,
  },
  {
    title: "Build Your Empire",
    description:
      "Turn your winnings into wealth. Invest wisely, expand your influence, and transform from hustler to business mogul. The streets made you, success defines you.",
    image: IMAGES.CARD_8
  },
];

const StoryCarousel = () => {
  const [emblaRef, emblaApi] = useEmblaCarousel({ loop: true });

  const scrollPrev = React.useCallback(() => emblaApi && emblaApi.scrollPrev(), [emblaApi]);
  const scrollNext = React.useCallback(() => emblaApi && emblaApi.scrollNext(), [emblaApi]);

  return (
    <div className="relative mb-8 group">
      <div className="overflow-hidden rounded-2xl" ref={emblaRef}>
        <div className="flex">
          {STORY_SLIDES.map((slide, idx) => (
            <div key={idx} className="relative flex-[0_0_100%] min-w-0">
              <div className="relative aspect-[2/1] overflow-hidden">
                <img
                  src={slide.image}
                  alt={slide.title}
                  className="absolute inset-0 w-full h-full object-cover"
                />
                {/* dark gradient overlay */}
                <div className="absolute inset-0 bg-black bg-opacity-60" />


                {/* Centered text wrapper */}
                <div className="absolute inset-0  items-center justify-center p-3 text-center">
                  <p
                    className="text-2xl font-bold text-white uppercase font-[Bebas Neue] "
                    style={{
                      textShadow: `-2px -2px 0 #000, 2px -2px 0 #000, -2px 2px 0 #000, 2px 2px 0 #000`,
                    }}
                  >
                    {slide.title}
                  </p>

                  <p className="mt-9 text-white/90 ">
                    {slide.description}
                  </p>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Navigation Buttons */}
      <button
        onClick={scrollPrev}
        className="absolute left-4 top-1/2 -translate-y-1/2 w-10 h-10 rounded-full bg-black/50 flex items-center justify-center text-white opacity-0 group-hover:opacity-100 transition-opacity"
      >
        <ChevronLeft size={24} />
      </button>
      <button
        onClick={scrollNext}
        className="absolute right-4 top-1/2 -translate-y-1/2 w-10 h-10 rounded-full bg-black/50 flex items-center justify-center text-white opacity-0 group-hover:opacity-100 transition-opacity"
      >
        <ChevronRight size={24} />
      </button>
    </div>
  );
};

export default StoryCarousel;
