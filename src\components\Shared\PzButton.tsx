import { FC } from 'react';

export const PzButton: FC<{
  type?: 'primary' | 'secondary';
  text: string;
  isDisabled?: boolean;
  onClick: React.MouseEventHandler<HTMLButtonElement>;
}> = ({ type = 'primary', text, isDisabled, onClick }) => (
  <button
    onClick={onClick}
    className={`w-full uppercase text-white font-[Anton] py-3 rounded-xl text-xl transition-colors ${
      type === 'primary'
        ? 'bg-[#ED0CFF] hover:bg-[#d30ae0]'
        : 'bg-gray-700 hover:bg-gray-600'
    } ${
      isDisabled
        ? 'bg-gray-600 cursor-not-allowed scale-95 hover:bg-gray-600'
        : 'transition-colors'
    }`}
    disabled={isDisabled}
  >
    {text}
  </button>
);
