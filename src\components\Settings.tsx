import React, { useState, useEffect } from 'react';
import { X, Bell, Volume2, Music, Moon, Sun, Globe, Lock, Shield, Smartphone, Mail, User } from 'lucide-react';
import TwoFactorAuth from './TwoFactorAuth';
import TrustedDevices from './TrustedDevices';
import ChangeEmail from './ChangeEmail';
import ProfileSettings from './ProfileSettings';
import useFetch from '../hooks/useFetch';
import { GET_USER_PROFILE } from '../api/auth';
import { useAuth } from '../auth/AuthContext';

interface SettingsProps {
  onClose: () => void;
}

const Settings: React.FC<SettingsProps> = ({ onClose }) => {
  const { accessToken } = useAuth();
  const [notificationsEnabled, setNotificationsEnabled] = useState(true);
  const [soundEnabled, setSoundEnabled] = useState(true);
  const [musicEnabled, setMusicEnabled] = useState(true);
  const [darkMode, setDarkMode] = useState(true);
  // const [language, setLanguage] = useState('en');
  const [show2FA, setShow2FA] = useState(false);
  const [showTrustedDevices, setShowTrustedDevices] = useState(false);
  const [showChangeEmail, setShowChangeEmail] = useState(false);
  const [showProfile, setShowProfile] = useState(false);
  const [isAnimating, setIsAnimating] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  // Fetch user profile data
  const { data: userProfile } = useFetch(GET_USER_PROFILE, {
    headers: {
      Authorization: `Bearer ${accessToken}`,
    },
  });
  console.log("🚀 ~ userProfile:", userProfile)


  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    window.addEventListener('keydown', handleEscape);
    return () => window.removeEventListener('keydown', handleEscape);
  }, [onClose]);

  // Check if mobile on mount and resize
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);

    // Trigger animation
    setTimeout(() => setIsAnimating(true), 50);

    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Mobile bottom sheet styles
  const mobileContainerClass = `
    fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-end justify-center
    transition-all duration-300 ease-out
    ${isAnimating ? 'opacity-100' : 'opacity-0'}
  `;

  const mobileContentClass = `
  bg-gradient-to-b from-[#510957] to-black
    rounded-t-3xl w-full max-h-[85vh] flex flex-col
    transform transition-transform duration-300 ease-out
    ${isAnimating ? 'translate-y-0' : 'translate-y-full'}
  `;

  // Desktop modal styles
  const desktopContainerClass = `
    fixed inset-0 bg-black/60 backdrop-blur-sm z-50 flex items-center justify-center p-4
    transition-all duration-300 ease-out
    ${isAnimating ? 'opacity-100' : 'opacity-0'}
  `;

  const desktopContentClass = `
   bg-[linear-gradient(to_bottom_right,_#510957_10%,_black_80%)]
  rounded-2xl w-full max-w-lg max-h-[85vh] flex flex-col
  transform transition-all duration-300 ease-out
  ${isAnimating ? 'scale-100 opacity-100' : 'scale-95 opacity-0'}
`;

  return (
    <>
      <div className={isMobile ? mobileContainerClass : desktopContainerClass}>
        <div className={isMobile ? mobileContentClass : desktopContentClass}>
          <div className="p-6  flex justify-between items-center sticky top-0  from-[#510957] to-[#510957]rounded-t-3xl md:rounded-t-2xl z-10">
            <h2 className="text-xl  text-white font-[Anton] tracking-wide">Settings</h2>
            <button
              onClick={onClose}
              className="text-white/60 hover:text-white transition-colors"
            >
              <X size={24} />
            </button>
          </div>

          <div className="flex-1 overflow-y-auto">
            <div className="p-6 space-y-6">
              {/* Profile Section */}
              <section>
                <h3 className="text-lg font-semibold mb-4 flex items-center gap-2 font-[Anton] tracking-wide font-[Anton] tracking-wide">
                  <User size={18}  className="text-[#ED0CFF]" />
                  Profile
                </h3>
                <button
                  onClick={() => setShowProfile(true)}
                  className="w-full flex items-center justify-between p-4 bg-[#131313] rounded-lg  transition-colors"
                >
                  <div className="flex items-center gap-3">
                    <img
                      src={userProfile?.profile_picture || "https://images.unsplash.com/photo-1531384441138-2736e62e0919?w=50&h=50&fit=crop"}
                      alt="Profile"
                      className="w-10 h-10 rounded-full border-2 border-yellow-400"
                    />
                    <div className="text-left">
                      <p className="font-semibold text-xs">Player ID: {userProfile?.user_id}</p>
                      <p className="text-sm text-white/60">{userProfile?.email || "Email"}</p>
                    </div>
                  </div>
                  <span className="text-sm text-white font-[Anton] tracking-wide">Edit</span>
                </button>
              </section>

              {/* Notifications */}
              <section>
                <h3 className="text-lg font-semibold mb-4 flex items-center gap-2 font-[Anton] tracking-wide">
                                        {/* <div className="bg-[#1c1c1c] p-3 rounded-lg"> */}
                  <Bell size={18}  className="text-white" />
                  {/* </div> */}
                  Notifications
                </h3>
                <div className="space-y-4 w-full  items-center justify-between p-4 bg-[#131313] rounded-lg  transition-colors">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium font-[Anton] tracking-wide">Push Notifications</p>
                      <p className="text-sm text-white/60">Receive game alerts and updates</p>
                    </div>
                    <button
                      onClick={() => setNotificationsEnabled(!notificationsEnabled)}
                      className={`w-12 h-6 rounded-full transition-colors ${notificationsEnabled ? 'bg-[#ED0CFF]' : 'bg-white/10'
                        }`}
                    >
                      <div className={`w-5 h-5 rounded-full bg-white transform transition-transform ${notificationsEnabled ? 'translate-x-6' : 'translate-x-1'
                        }`} />
                    </button>
                  </div>
                </div>
              </section>


              {/* Security */}
              <section>
                <h3 className="text-lg font-semibold mb-4 flex items-center gap-2 font-[Anton] tracking-wide">
                   {/* <div className="bg-[#1c1c1c] p-3 rounded-lg"> */}
                  <Lock size={18}  className="text-white" />
                  {/* </div> */}
                  Security
                </h3>
                <div className="space-y-4">
                  <button
                    onClick={() => setShow2FA(true)}
                    className="w-full flex items-center justify-between p-4 bg-[#131313] rounded-lg  transition-colors"
                  >
                    <div className="flex items-center gap-3 ">
                      <div className="bg-[#1c1c1c] p-3 rounded-lg">
                        <Shield size={18} className="text-[#ED0CFF]" />
                      </div>
                      <div className="text-left">
                        <p className="font-medium font-[Anton] tracking-wide">Two-Factor Authentication</p>
                        <p className="text-sm text-white/60">Add extra security to your account</p>
                      </div>
                    </div>
                    <div className="text-sm text-white/40">Not Enabled</div>
                  </button>

                </div>
              </section>

              {/* Account */}
              <section>
                <h3 className="text-lg font-semibold mb-4 flex items-center gap-2 font-[Anton] tracking-wide">
                  {/* <div className="bg-[#1c1c1c] p-3 rounded-lg"> */}
                    <Mail size={18} className="text-white" />
                  {/* </div> */}
                  Account

                </h3>
                <div className="space-y-4">
                  <button
                    onClick={() => setShowChangeEmail(true)}
                    className="w-full flex items-center justify-between p-4 bg-[#131313] rounded-lg  transition-colors"
                  >
                    <div className="flex items-center gap-3">
                      <div className="bg-[#1c1c1c] p-3 rounded-lg">
                        <Mail size={18} className="text-[#ED0CFF]" />
                      </div>
                      <div className="text-left">
                        <p className="font-medium font-[Anton] tracking-wide">Change Email</p>
                        <p className="text-sm text-white/60">Update your email address</p>
                      </div>
                    </div>
                  </button>

                  <button className="w-full flex items-center justify-between p-4 bg-[#131313] rounded-lg  transition-colors">
                    <div className="flex items-center gap-3">
                      <div className="bg-[#1c1c1c] p-3 rounded-lg">
                        <Lock size={18} className="text-[#ED0CFF]" />
                      </div>
                      <div className="text-left">
                        <p className="font-medium font-[Anton] tracking-wide">Change Password</p>
                        <p className="text-sm text-white/60">Update your password</p>
                      </div>
                    </div>
                  </button>
                </div>
              </section>
            </div>
          </div>
        </div>
      </div>

      {/* 2FA Modal */}
      {show2FA && (
        <TwoFactorAuth onClose={() => setShow2FA(false)} data={userProfile} />
      )}

      {/* Trusted Devices Modal */}
      {showTrustedDevices && (
        <TrustedDevices onClose={() => setShowTrustedDevices(false)} />
      )}

      {/* Change Email Modal */}
      {showChangeEmail && (
        <ChangeEmail onClose={() => setShowChangeEmail(false)} />
      )}

      {/* Profile Settings Modal */}
      {showProfile && (
        <ProfileSettings onClose={() => setShowProfile(false)} />
      )}
    </>
  );
};

export default Settings;