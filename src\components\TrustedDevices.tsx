import React, { useState } from 'react';
import { X, Smartphone, Laptop, Globe, Clock, MoreVertical, Trash2, Shield } from 'lucide-react';

interface TrustedDevicesProps {
  onClose: () => void;
}

interface Device {
  id: string;
  name: string;
  type: 'mobile' | 'desktop' | 'tablet';
  browser: string;
  location: string;
  lastActive: string;
  isCurrentDevice: boolean;
}

const MOCK_DEVICES: Device[] = [
  {
    id: '1',
    name: 'iPhone 13 Pro',
    type: 'mobile',
    browser: 'Safari',
    location: 'Lagos, Nigeria',
    lastActive: 'Active now',
    isCurrentDevice: true
  },
  {
    id: '2',
    name: 'MacBook Pro',
    type: 'desktop',
    browser: 'Chrome',
    location: 'Lagos, Nigeria',
    lastActive: '2 hours ago',
    isCurrentDevice: false
  },
  {
    id: '3',
    name: 'iPad Air',
    type: 'tablet',
    browser: 'Safari',
    location: 'Abuja, Nigeria',
    lastActive: '3 days ago',
    isCurrentDevice: false
  }
];

const TrustedDevices: React.FC<TrustedDevicesProps> = ({ onClose }) => {
  const [devices, setDevices] = useState<Device[]>(MOCK_DEVICES);
  const [selectedDevice, setSelectedDevice] = useState<Device | null>(null);
  const [showConfirmRemove, setShowConfirmRemove] = useState(false);

  const handleRemoveDevice = (device: Device) => {
    if (device.isCurrentDevice) {
      return;
    }
    setSelectedDevice(device);
    setShowConfirmRemove(true);
  };

  const confirmRemoveDevice = () => {
    if (selectedDevice) {
      setDevices(devices.filter(d => d.id !== selectedDevice.id));
      setShowConfirmRemove(false);
      setSelectedDevice(null);
    }
  };

  const getDeviceIcon = (type: Device['type']) => {
    switch (type) {
      case 'mobile':
        return Smartphone;
      case 'desktop':
        return Laptop;
      case 'tablet':
        return Laptop;
      default:
        return Globe;
    }
  };

  return (
    <div className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-gradient-to-b from-purple-900 to-indigo-900 rounded-2xl w-full max-w-lg">
        <div className="p-6 border-b border-white/10 flex justify-between items-center">
          <h2 className="text-xl font-bold">Trusted Devices</h2>
          <button 
            onClick={onClose}
            className="text-white/60 hover:text-white transition-colors"
          >
            <X size={24} />
          </button>
        </div>

        <div className="p-6">
          <div className="bg-white/5 rounded-xl p-4 mb-6">
            <div className="flex items-center gap-3">
              <div className="bg-yellow-500/20 p-2 rounded-lg">
                <Shield size={20} className="text-yellow-400" />
              </div>
              <p className="text-sm text-white/60">
                These are the devices that have been authorized to access your account. You can remove access from devices you no longer use.
              </p>
            </div>
          </div>

          <div className="space-y-4">
            {devices.map(device => {
              const IconComponent = getDeviceIcon(device.type);
              return (
                <div
                  key={device.id}
                  className="bg-white/5 rounded-xl p-4 flex items-center justify-between group"
                >
                  <div className="flex items-center gap-4">
                    <div className="bg-white/10 p-3 rounded-lg">
                      <IconComponent size={24} className="text-white/60" />
                    </div>
                    <div>
                      <div className="flex items-center gap-2">
                        <h3 className="font-medium">{device.name}</h3>
                        {device.isCurrentDevice && (
                          <span className="text-xs bg-green-500/20 text-green-400 px-2 py-0.5 rounded-full">
                            Current Device
                          </span>
                        )}
                      </div>
                      <div className="text-sm text-white/60 space-y-1">
                        <p>{device.browser} • {device.location}</p>
                        <div className="flex items-center gap-1">
                          <Clock size={14} />
                          <span>{device.lastActive}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <div className="relative">
                    <button
                      onClick={() => handleRemoveDevice(device)}
                      className={`p-2 rounded-lg transition-colors ${
                        device.isCurrentDevice
                          ? 'text-white/20 cursor-not-allowed'
                          : 'text-white/60 hover:bg-white/10 hover:text-white'
                      }`}
                      disabled={device.isCurrentDevice}
                    >
                      <Trash2 size={20} />
                    </button>
                  </div>
                </div>
              );
            })}
          </div>

          {devices.length === 0 && (
            <div className="text-center py-8 text-white/60">
              <Globe size={48} className="mx-auto mb-4 opacity-40" />
              <p>No trusted devices</p>
            </div>
          )}
        </div>
      </div>

      {/* Confirmation Modal */}
      {showConfirmRemove && selectedDevice && (
        <div className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <div className="bg-gradient-to-b from-purple-900 to-indigo-900 rounded-2xl p-6 w-full max-w-sm">
            <h3 className="text-xl font-bold mb-4">Remove Device?</h3>
            <p className="text-white/60 mb-6">
              Are you sure you want to remove "{selectedDevice.name}" from your trusted devices? You'll need to re-authenticate if you use this device again.
            </p>
            <div className="flex gap-4">
              <button
                onClick={() => setShowConfirmRemove(false)}
                className="flex-1 py-2 px-4 rounded-lg bg-white/10 hover:bg-white/20 transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={confirmRemoveDevice}
                className="flex-1 py-2 px-4 rounded-lg bg-red-500 hover:bg-red-600 transition-colors"
              >
                Remove
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default TrustedDevices;