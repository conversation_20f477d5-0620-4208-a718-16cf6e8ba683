import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { ChevronLeft, Users, Share2, Copy, Plus, MapIcon as WhatsappIcon, Mail, MessageSquare, ExternalLink, CheckCircle, X, Trophy, Star, Target, TrendingUp, Info, Delete, Trash, } from 'lucide-react';

import { Formik, Form, Field, ErrorMessage } from 'formik';
import Whatsaap from '../assets/images/wapp.svg';
import TeliGram from '../assets/images/Artboard.svg';
import email from '../assets/images/email.svg';
import * as Yup from 'yup';
import { IMAGES } from '../constant/image';
import { createSquad, searchSquad } from '../api/squad';
import { useAuth } from '../auth/AuthContext';
import { CustomToast } from '../utils/validations/customeToast';


interface ScuadType {
  handle: string,
  type: string,

}

interface Squad {
  id: string;
  name: string;
  members: {
    username: string;
    avatar: string;
    joinedAt: Date;
    contribution: number;
    rank: number;
    winRate: number;
  }[];
  createdAt: Date;
  stats: {
    totalEarnings: number;
    gamesPlayed: number;
    winRate: number;
    weeklyGrowth: number;
    rank: number;
  };
  recentActivity: {
    id: string;
    type: 'win' | 'achievement' | 'member_joined';
    description: string;
    timestamp: Date;
    reward?: number;
  }[];
}

const MOCK_SQUADS: Squad[] = [
  {
    id: 'squad-1',
    name: 'Street Kings',
    members: [
      {
        username: 'seun',
        avatar: 'https://images.unsplash.com/photo-1531384441138-2736e62e0919?w=100&h=100&fit=crop',
        joinedAt: new Date('2025-03-01'),
        contribution: 5000,
        rank: 1,
        winRate: 75
      },
      {
        username: 'chioma_wins',
        avatar: 'https://images.unsplash.com/photo-1531123897727-8f129e1688ce?w=100&h=100&fit=crop',
        joinedAt: new Date('2025-03-02'),
        contribution: 3500,
        rank: 2,
        winRate: 68
      }
    ],
    createdAt: new Date('2025-03-01'),
    stats: {
      totalEarnings: 12500,
      gamesPlayed: 156,
      winRate: 30,
      weeklyGrowth: 15,
      rank: 3
    },
    recentActivity: [
      {
        id: '1',
        type: 'win',
        description: 'Squad won Street Hustle Tournament',
        timestamp: new Date('2025-03-10T14:00:00'),
        reward: 2500
      },
      {
        id: '2',
        type: 'achievement',
        description: 'Achieved 100 squad wins',
        timestamp: new Date('2025-03-09T16:30:00'),
        reward: 1000
      },
      {
        id: '3',
        type: 'member_joined',
        description: 'chioma_wins joined the squad',
        timestamp: new Date('2025-03-02T10:15:00')
      }
    ]
  }
];

const SHARE_OPTIONS = [
  {
    id: 'whatsapp',
    name: 'WhatsApp',
    icon: Whatsaap,
    color: 'text-green-400',
    bgColor: 'bg-green-400/20'
  },
  {
    id: 'telegram',
    name: 'Telegram',
    icon: TeliGram,
    color: 'text-blue-400',
    bgColor: 'bg-blue-400/20'
  },
  {
    id: 'email',
    name: 'Email',
    icon: email,
    color: 'text-purple-400',
    bgColor: 'bg-purple-400/20'
  }
];


const DUMMY_RANKING_DATA = [
  { id: 'squad-1', name: 'Street Kings', bucksLast6Days: 4_500 },
  { id: 'squad-2', name: 'Night Riders', bucksLast6Days: 3_800 },
  { id: 'squad-3', name: 'Urban Legends', bucksLast6Days: 2_900 },
  { id: 'squad-4', name: 'Grind Masters', bucksLast6Days: 1_250 },
  { id: 'squad-5', name: 'Cash Cowboys', bucksLast6Days: 750 },
];


interface ProgressionRow {
  level: number;
  rankLabel: string;
  cumulative: string;
  reward: string;
  notes: string;
}
const PROGRESSION_DATA: ProgressionRow[] = [
  { level: 1, rankLabel: 'Rookies', cumulative: '0', reward: '🐣 Newbie Badge', notes: 'Default' },
  { level: 2, rankLabel: 'Hustlers', cumulative: '10,000', reward: '🎟 1x Free Scratch Card', notes: '🎯 Target Icon' },
  { level: 3, rankLabel: 'Grinders', cumulative: '25,000', reward: '🎡 1x Free WoF ⚡ Lightning Bolt', notes: '' },
  { level: 4, rankLabel: 'Risk Takers', cumulative: '50,000', reward: '💠 1x Rise Coin 🎲 Golden Dice', notes: 'Rare currency' },
  { level: 5, rankLabel: 'High Rollers', cumulative: '100,000', reward: '💰 25 Bucks 💎 Diamond Crown', notes: 'Airdropped directly' },
  { level: 6, rankLabel: 'Money Makers', cumulative: '250,000', reward: '🎟 Scratch + 🎡 Spin 🔥 Fire Badge', notes: 'Combo reward' },
  { level: 7, rankLabel: 'Big Shots', cumulative: '500,000', reward: '💠 2x Rise Coins 🏆 Trophy Icon', notes: '' },
  { level: 8, rankLabel: 'Whale Squad', cumulative: '1,000,000', reward: '💰 50 Bucks + 🎡 Elite Wheel Spin', notes: '🐋 Whale Avatar — better loot' },
  { level: 9, rankLabel: 'Legends', cumulative: '2,000,000', reward: '🎖 Cosmetic: Squad Banner + 👑 Royal Crown', notes: 'Displayed in leaderboards' },
];


const ProgressionPopup: React.FC<{ data: ProgressionRow[]; onClose: () => void }> = ({ data, onClose }) => {
  const [isMobile, setIsMobile] = useState(false);
  const [isAnimating, setIsAnimating] = useState(false);

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768);
    };
    handleResize();
    window.addEventListener("resize", handleResize);

    // Animate in
    setTimeout(() => setIsAnimating(true), 50);

    return () => window.removeEventListener("resize", handleResize);
  }, []);

  const mobileContainerClass = `
    fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-end justify-center
    transition-all duration-300 ease-out ${isAnimating ? 'opacity-100' : 'opacity-0'}
  `;

  const mobileContentClass = `
    bg-gradient-to-b from-[#510957] to-black rounded-t-3xl w-full max-h-[85vh] flex flex-col
    transform transition-transform duration-300 ease-out ${isAnimating ? 'translate-y-0' : 'translate-y-full'}
  `;

  const desktopContainerClass = `
    fixed inset-0 bg-black/60 backdrop-blur-sm z-50 flex items-center justify-center
    transition-opacity duration-300 ease-out ${isAnimating ? 'opacity-100' : 'opacity-0'}
  `;

  const desktopContentClass = `
    bg-gradient-to-br from-[#510957] to-black rounded-2xl w-full max-w-2xl p-6 relative
    transform transition-all duration-300 ease-out ${isAnimating ? 'scale-100 opacity-100' : 'scale-95 opacity-0'}
  `;

  return (
    <div className={isMobile ? mobileContainerClass : desktopContainerClass}>
      <div className={isMobile ? mobileContentClass : desktopContentClass}>
        <div className="p-6 flex justify-between items-center sticky top-0 rounded-t-3xl z-10">
          <h3 className="text-white text-xl font-[Anton] tracking-wide">🧗 Squad Level Progression</h3>
          <button onClick={onClose} className="text-white/60 hover:text-white">
            <X size={24} />
          </button>
        </div>

        <div className="overflow-y-auto px-4 pb-6">
          <table className="min-w-full bg-white/5 rounded-lg overflow-hidden text-white text-sm">
            <thead className="bg-white/10">
              <tr>
                <th className="px-4 py-2 text-left">Level</th>
                <th className="px-4 py-2 text-left">Rank</th>
                <th className="px-4 py-2 text-left">Cumulative</th>
                <th className="px-4 py-2 text-left">Reward</th>
                {/* <th className="px-4 py-2 text-left">Notes</th> */}
              </tr>
            </thead>
            <tbody>
              {data.map(row => (
                <tr key={row.level} className="border-t border-white/10 hover:bg-white/10">
                  <td className="px-4 py-2">{row.level}</td>
                  <td className="px-4 py-2">{row.rankLabel}</td>
                  <td className="px-4 py-2">{row.cumulative}</td>
                  <td className="px-4 py-2">{row.reward}</td>
                  {/* <td className="px-4 py-2">{row.notes}</td> */}
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};


const LevelUpPopup: React.FC<{ level: number; onClose: () => void }> = ({ level, onClose }) => {
  return (
    <div className="fixed inset-0 bg-black/60 backdrop-blur-sm z-50 flex items-center justify-center">
      <div className="relative bg-[linear-gradient(to_bottom_right,_#510957_10%,_black_80%)] rounded-2xl p-6 max-w-md w-full text-center transform transition-all duration-300 scale-100 opacity-100">
        {/* Close icon at top-right */}
        <button
          onClick={onClose}
          className="absolute top-4 right-4 text-white hover:text-yellow-300 focus:outline-none"
          aria-label="Close Level Up Popup"
        >
          <X size={24} />
        </button>

        <h2 className="text-3xl font-bold text-yellow-300 mb-4">🎉 Level Up! 🎉</h2>
        <p className="text-white text-lg mb-6">Your squad has reached Level {level}!</p>
        <button
          onClick={onClose}
          className="px-6 py-2 bg-yellow-300 text-black font-semibold rounded-lg hover:bg-yellow-400 transition-colors"
        >
          Close
        </button>
      </div>
    </div>
  );
};


const CREATE_SQUAD_SCHEMA = Yup.object().shape({
  name: Yup.string()
    .min(2, 'Too Short!')
    .max(50, 'Too Long!')
    .required('Squad Name is required'),
  joiningType: Yup.string()
    .oneOf(['Open', 'Request to Join', 'Invite-only'], 'Invalid joining type')
    .required('Joining Type is required'),
});

interface CreateSquadFormValues {
  name: string;
  joiningType: string;
}


interface HeroBannerProps {
  amountRemaining: number;
  nextLevel: number;
  backgroundUrl: string;    // URL for your banner background
}

const HeroBanner: React.FC<HeroBannerProps> = ({
  amountRemaining,
  nextLevel,
  backgroundUrl,
}) => (
  <div
    className="relative w-full h-40 rounded-2xl overflow-hidden mb-6"
    style={{ backgroundImage: `url(${backgroundUrl})`, backgroundSize: 'cover', backgroundPosition: 'center' }}
  >
    {/* Dark overlay */}
    <div className="absolute inset-0 bg-black/50"></div>

    {/* Content */}
    <div className="relative z-10 flex flex-col sm:flex-row items-center justify-between h-full px-6">
      <h2 className="text-white font-[Anton] text-xl sm:text-2xl">
        Only&nbsp;
        <span className="font-bold text-yellow-300">₦{amountRemaining.toLocaleString()}</span>
        &nbsp;more to&nbsp;
        <span className="font-bold text-yellow-300">Level {nextLevel}</span>
      </h2>
      <p className="mt-2 sm:mt-0 text-sm sm:text-base text-white/80 italic">
        — unlock combo rewards for everyone!
      </p>
    </div>
  </div>
);


const SquadsPage = () => {
  const navigate = useNavigate();
  const { accessToken } = useAuth()
  const [squads, setSquads] = useState<Squad[]>(MOCK_SQUADS);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [copied, setCopied] = useState(false);
  const [showShareModal, setShowShareModal] = useState(false);
  const [squad, setSquad] = useState<Squad | null>(squads[0] || null);
  const inviteLink = squad ? `https://playzuzu.com/jjoin/${squad.id}` : '';
  const [isAnimating, setIsAnimating] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const [showProgression, setShowProgression] = useState(false);
  const [rankingData, setRankingData] = useState(DUMMY_RANKING_DATA)
  const [showLevelUp, setShowLevelUp] = useState(false);
  const [leveledTo, setLeveledTo] = useState(1);
  const [addUserId, setAddUserId] = useState('');
  const [showAddMemberPopup, setShowAddMemberPopup] = useState(false);
  // For squad search/join
  const [searchTerm, setSearchTerm] = useState('');
  const [searchError, setSearchError] = useState('');
  // Compute hero values
  const nextLevel = 7; // replace with dynamic logic as needed
  const thresholdForNext = 30000; // e.g. level thresholds map
  const currentPoints = squad?.stats.totalEarnings || 0;
  const amountRemaining = Math.max(thresholdForNext - currentPoints, 0);

  const [squadResult, setSquadResult] = useState<ScuadType | null>(null);
  const [loading, setLoading] = useState(false);




  useEffect(() => {
    const delayDebounce = setTimeout(() => {
      if (searchTerm.trim().length > 0) {
        fetchSquadByName(searchTerm);
      }
    }, 500); // 500ms debounce

    return () => clearTimeout(delayDebounce);
  }, [searchTerm]);

  const fetchSquadByName = async (name: string) => {
    try {
      setLoading(true)
      if (!accessToken) throw new Error('Access token missing');

      const response = await searchSquad({ name }, accessToken)
      setSquadResult(response);
    } catch (error) {
      console.log(error)
    } finally {
      setLoading(false)
    }
  }

  // Trigger level-up popup
  useEffect(() => {
    if (amountRemaining === 0 && !showLevelUp) {

      setLeveledTo(nextLevel);
      setShowLevelUp(true);
    }
  }, [amountRemaining, nextLevel, showLevelUp]);

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);

    // Trigger animation
    setTimeout(() => setIsAnimating(true), 50);

    return () => window.removeEventListener('resize', checkMobile);
  }, []);


  const handleCopyLink = () => {
    navigator.clipboard.writeText(inviteLink);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  const handleShare = (platform: string) => {
    let shareUrl = '';
    const message = `Join my squad on Playzuzu! ${inviteLink}`;

    switch (platform) {
      case 'whatsapp':
        shareUrl = `https://wa.me/?text=${encodeURIComponent(message)}`;
        break;
      case 'telegram':
        shareUrl = `https://t.me/share/url?url=${encodeURIComponent(inviteLink)}&text=${encodeURIComponent('Join my squad on Playzuzu!')}`;
        break;
      case 'email':
        shareUrl = `mailto:?subject=Join my Playzuzu squad&body=${encodeURIComponent(message)}`;
        break;
    }

    window.open(shareUrl, '_blank');
  };
  const handleCreateSquad = async (
    values: CreateSquadFormValues,
    { resetForm }: { resetForm: () => void }
  ) => {
    try {
      if (!accessToken) throw new Error('Access token missing');

      const payload = {
        created_at: new Date().toISOString(), // Use ISO format for date
        handle: values.name,
        type: values.joiningType
      };

      const response = await createSquad(payload, accessToken);

      // Optional: Handle success response (e.g., show toast or update UI)
      if (response?.message) {

        CustomToast('success', "Squad Create Successfully.");
      }

      resetForm();
      setShowCreateModal(false);
    } catch (err) {
      console.error('Failed to create squad:', err);
      // Optional: Show error message to user (toast, modal, etc.)
    }
  };


  const mobileContainerClass = `
     fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-end justify-center
     transition-all duration-300 ease-out
     ${isAnimating ? 'opacity-100' : 'opacity-0'}
   `;

  const mobileContentClass = `
   bg-gradient-to-b from-[#510957] to-black
     rounded-t-3xl w-full max-h-[85vh] flex flex-col
     transform transition-transform duration-300 ease-out
     ${isAnimating ? 'translate-y-0' : 'translate-y-full'}
   `;

  // Desktop modal styles
  const desktopContainerClass = `
     fixed inset-0 bg-black/60 backdrop-blur-sm z-50 flex items-center justify-center p-4
     transition-all duration-300 ease-out
     ${isAnimating ? 'opacity-100' : 'opacity-0'}
   `;

  const desktopContentClass = `
    bg-[linear-gradient(to_bottom_right,_#510957_10%,_black_80%)]
   rounded-2xl w-full max-w-lg max-h-[85vh] flex flex-col
   transform transition-all duration-300 ease-out
   ${isAnimating ? 'scale-100 opacity-100' : 'scale-95 opacity-0'}
 `;

  // Add Member by User ID logic
  const DEFAULT_MEMBER_AVATAR = 'https://ui-avatars.com/api/?background=random&name=User';
  const handleAddMember = () => {
    if (!addUserId.trim() || !squad) return;
    // Prevent duplicate usernames
    if (squad.members.some(m => m.username === addUserId.trim())) {
      alert('User already in squad!');
      return;
    }
    const newMember = {
      username: addUserId.trim(),
      avatar: DEFAULT_MEMBER_AVATAR,
      joinedAt: new Date(),
      contribution: 0,
      rank: squad.members.length + 1,
      winRate: 0,
    };
    const updatedSquad = { ...squad, members: [...squad.members, newMember] };
    setSquads([updatedSquad, ...squads.slice(1)]);
    setAddUserId('');
  };

  console.log("🚀 ~ SquadsPage ~ squadResult:", squadResult)


  return (
    <div className="max-w-4xl mx-auto">

      {/* Display Squad Level if in a squad */}
      {squad && (
        <>
          <HeroBanner backgroundUrl={IMAGES.SQUAD_BANNER_IMAGE} amountRemaining={amountRemaining} nextLevel={nextLevel} />
          <div className="flex items-center gap-2 mb-2 px-2">
            <span className="text-white/80 text-sm font-[Anton]">Squad Level:</span>
            <span className="text-[#ED0CFF] font-bold text-lg font-[Anton]">
              {squad.stats.rank}
            </span>
            <span className="text-white/50 text-xs">({PROGRESSION_DATA[squad.stats.rank - 1]?.rankLabel || 'Unknown'})</span>
            <button
              onClick={() => {
                if (window.confirm('Are you sure you want to leave your current squad?')) {
                  setSquad(null);
                }
              }}
              className="ml-4 px-3 py-1 rounded-lg bg-[#ED0CFF] text-white text-xs font-[Anton] hover:bg-[#d30ae0] transition-colors"
              title="Leave Squad"
            >
              Leave Squad
            </button>
          </div>
        </>
      )}

      <div className="mb-4 px-2 bg-white/10 rounded-lg p-4 border border-[#ED0CFF]">
        <p className="text-[14px] text-white mb-1 tracking-wide font-[Anton]">Level Progression</p>
        <div className="w-full bg-white/10 rounded-full h-2">
          <div
            className="h-2 rounded-full bg-green-400"
            style={{ width: `${Math.min(100, (currentPoints / thresholdForNext) * 100)}%` }}
          />
        </div>
        <p className="text-[12px] text-white mt-1 font-[Anton] tracking-wide">
          {currentPoints.toLocaleString()} / {thresholdForNext.toLocaleString()} Bucks to next level
        </p>
      </div>



      <h1 className="text-[20px] mt-2 tracking-wide font-bold w-auto font-[Anton]">Squads</h1>

      <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-4 mt-4 mb-6 w-full">
        {/* Show search/join only if NOT in a squad */}
        {!squad && (
          <>
            {/* Search + Result */}
            <form
              onSubmit={(e) => {
                e.preventDefault();
                if (!searchTerm.trim()) return;
                const found = squads.find(
                  (s) =>
                    s.name.toLowerCase() === searchTerm.trim().toLowerCase()
                );
                if (found) {
                  setSquad(found);
                  setSearchError('');
                } else {
                  setSearchError('No squad found with that name.');
                }
              }}
              className="flex-1 max-w-lg mx-auto w-full"
            >
              <div className="flex flex-col">
                {/* Input Row */}
                <div className="flex items-center gap-2">
                  <input
                    type="text"
                    placeholder="Search or join squad by name"
                    value={searchTerm}
                    onChange={(e) => {
                      setSearchTerm(e.target.value);
                      setSearchError('');
                      // if using debounce you can trigger fetchSquadByName here
                    }}
                    className="flex-1 px-4 py-3 rounded-lg bg-white/10 border border-white/20 text-white placeholder-white/40 focus:outline-none focus:ring-2 focus:ring-yellow-400 transition"
                    aria-label="Search squad by name"
                  />
                </div>

                {/* Error or Spacer */}
                <div className="mt-2 min-h-[20px]">
                  {searchError ? (
                    <p className="text-red-400 text-sm">{searchError}</p>
                  ) : (
                    <p className="invisible text-sm">Placeholder</p>
                  )}
                </div>

                {/* Result Card */}
                {squadResult && (
                  <div className="mt-4 p-4 bg-white/10 border border-white/20 rounded-lg flex items-center justify-between text-white">
                    <div>
                      <p className="text-sm opacity-80">Squad Found:</p>
                      <p className="font-bold text-lg">
                        {squadResult.handle}
                      </p>
                      <p className="text-xs opacity-70">
                        {squadResult.type} Squad
                      </p>
                    </div>
                    <button
                      // onClick={() => joinSquad(squadResult.id)}
                      className="px-3 py-2 bg-yellow-400 text-black rounded-lg font-medium hover:bg-yellow-500 transition"
                      aria-label={`Join ${squadResult.handle}`}
                    >
                      Join
                    </button>
                  </div>
                )}
              </div>
            </form>

            {/* Create Squad */}
            <div className="flex justify-center sm:justify-end">
              <button
                onClick={() => setShowCreateModal(true)}
                className="px-4 py-3 rounded-lg flex items-center gap-2 text-[#ED0CFF] border border-[#ED0CFF] hover:bg-[#ED0CFF]/10 transition"
              >
                <Plus size={20} />
                <span className="uppercase font-[Anton] text-[14px] tracking-wide">
                  Create Squad
                </span>
              </button>
            </div>
          </>
        )}
      </div>




      {!squad && (
        <div className="text-center py-12">
          <div className="w-16 h-16 bg-white/10 rounded-full flex items-center justify-center mx-auto mb-4">
            <Users size={32} className="text-white/60" />
          </div>
          <h2 className="text-xl font-bold mb-2">No Squads Yet</h2>
          <p className="text-white/60 mb-6">Create a squad to play together and earn rewards!</p>
        </div>
      )}

      {squad && (
        <div className="bg-[#131313] rounded-xl p-4 mb-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-[14px] font-[Anton] truncate">{squad.name}</h3>
            <div className="flex items-center space-x-2">
              <button
                onClick={() => setShowShareModal(true)}
                className="transition-colors p-2 rounded-lg hover:bg-white/10"
                aria-label="Share Squad"
              >
                <Share2 size={20} className="text-[#ED0CFF]" />
              </button>
              <button
                onClick={() => setShowProgression(true)}
                className="transition-colors p-2 rounded-lg hover:bg-white/10"
                aria-label="Compare Squads"
              >
                <Info size={20} className="text-white/80 hover:text-white" />
              </button>
            </div>
          </div>

          <div className="flex items-center gap-2 mb-4 ">
            {squad.members.map((member, idx) => (
              <img key={member.username} src={member.avatar} alt={member.username} className="w-10 h-10 rounded-full" style={{ marginLeft: idx > 0 ? '-20px' : 0 }} />
            ))}
            <div className="ml-2">
              <p className="text-[14px] font-[Anton]">{squad.members.length} members</p>
              <p className="text-[12px] text-white/60">Created: {squad.createdAt.toLocaleDateString()}</p>
            </div>
          </div>

          <div className="border-t border-white/10 pt-4">
            <div className="flex items-center justify-between mb-4">
              <div className="text-white text-[14px] font-[Anton]">
                {squad.stats.totalEarnings.toLocaleString()} FPP
              </div>
            </div>

            {/* Inline Details */}
            <div>

              {/* <h3 className="text-lg font-semibold mb-4 font-[Anton] tracking-wide">Squad Progress</h3> */}


              <div className="mb-8">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold font-[Anton] tracking-wide">Squad Members</h3>
                  <button
                    onClick={() => setShowAddMemberPopup(true)}
                    className="flex items-center gap-2 bg-[#ED0CFF] px-3 py-1 rounded-lg hover:bg-[#d30ae0  ] transition-colors text-white text-sm font-[Anton]"
                    title="Add member by ID"
                  >
                    <Plus size={18} />
                    CREATE SQUAD
                  </button>
                </div>
                {/* Add Member by User ID Popup */}
                {showAddMemberPopup && (
                  <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/60 backdrop-blur-sm">
                    <div className="bg-[linear-gradient(to_bottom_right,_#510957_10%,_black_80%)] rounded-2xl p-6 w-full max-w-xs shadow-lg relative">
                      <button
                        onClick={() => setShowAddMemberPopup(false)}
                        className="absolute top-3 right-3 text-white/60 hover:text-white"
                        aria-label="Close Add Member Popup"
                      >
                        <X size={22} />
                      </button>
                      <h4 className="text-white text-lg font-[Anton] mb-4">Add Member by ID</h4>
                      <label htmlFor="add-member-by-id-input" className="block text-sm font-medium mb-2 text-white/80">User ID</label>
                      <input
                        id="add-member-by-id-input"
                        type="text"
                        value={addUserId}
                        onChange={e => setAddUserId(e.target.value)}
                        placeholder="Enter User ID"
                        className="w-full px-3 py-2 rounded-lg bg-white/10 border border-white/20 text-white placeholder-white/40 focus:outline-none focus:border-blue-400 mb-4"
                        title="User ID"
                        autoFocus
                      />
                      <button
                        onClick={() => { handleAddMember(); setShowAddMemberPopup(false); }}
                        className="w-full flex items-center justify-center gap-2 bg-[#ED0CFF] px-4 py-2 rounded-lg hover:bg-[#d30ae0] transition-colors disabled:opacity-50"
                        disabled={!addUserId.trim()}
                        title="Add member by ID"
                      >
                        <Plus size={20} className="text-white" />
                        <span className="text-white font-[Anton]">Add Member</span>
                      </button>
                    </div>
                  </div>
                )}
                <div className="space-y-4">
                  {squad.members.map(member => (
                    <div key={member.username} className="bg-white/5 rounded-lg p-4 flex items-center justify-between border border-[#ED0CFF]">
                      <div className="flex items-center gap-3">
                        <img src={member.avatar} alt={member.username} className="w-10 h-10 rounded-full" />
                        <div>
                          <p className="text-[14px] font-medium font-[Anton]">@{member.username}</p>
                          <p className="text-[12px] text-white/60">Joined {member.joinedAt.toLocaleDateString()}</p>
                        </div>
                      </div>
                      <div className="flex flex-col items-end space-y-1">
                        <button
                          onClick={() => window.confirm('Are you sure you want to remove this member?')}
                          className="p-1 rounded hover:bg-white/10 focus:outline-none"
                          aria-label="Remove member"
                        >
                          <Trash size={20} className="text-red-500" />
                        </button>
                        <p className="font-medium font-[Anton] text-white">
                          {member.contribution.toLocaleString()} Bucks
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              <div>
                <h3 className="text-lg font-semibold mb-4 font-[Anton] tracking-wide">Recent Activity</h3>
                <div className="space-y-4">
                  {squad.recentActivity.map(act => (
                    <div key={act.id} className="bg-white/5 rounded-lg p-4 flex items-center justify-between border border-[#ED0CFF]">
                      <div className="flex items-center gap-3">
                        {act.type === 'win' && <Trophy size={20} className="text-yellow-400" />}
                        {act.type === 'achievement' && <Star size={20} className="text-purple-400" />}
                        {act.type === 'member_joined' && <Users size={20} className="text-blue-400" />}
                        <div>
                          <p className="text-[14px] font-medium font-[Anton] tracking-wide">{act.description}</p>
                          <p className="text-[12px] text-white/60">{act.timestamp.toLocaleString()}</p>
                        </div>
                      </div>
                      {act.reward && <span className="text-white font-[Anton] tracking-wide">+{act.reward} Bucks</span>}
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {rankingData.length > 0 && (
        <div className="mb-6 px-2 bg-white/10 rounded-lg p-4">
          <h2 className="text-white font-[Anton] text-lg mb-4 tracking-wide">
            Tournament Style Rankings (Last 6 Days)
          </h2>
          <table className="min-w-full bg-white/5 rounded-lg overflow-hidden text-white text-sm">
            <thead className="bg-white/10">
              <tr>
                <th className="px-4 py-2 text-left">Rank</th>
                <th className="px-4 py-2 text-left">Squad</th>
                <th className="px-4 py-2 text-left">Bucks Won</th>
              </tr>
            </thead>
            <tbody>
              {rankingData.map((row, idx) => (
                <tr
                  key={row.id}
                  className="border-t border-white/10 hover:bg-white/10"
                >
                  <td className="px-4 py-2">{idx + 1}</td>
                  <td className="px-4 py-2">{row.name}</td>
                  <td className="px-4 py-2">
                    {row.bucksLast6Days.toLocaleString()}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
      {showLevelUp && (
        <LevelUpPopup level={leveledTo} onClose={() => setShowLevelUp(false)} />
      )}

      {/* Comparison Popup */}
      {showProgression && <ProgressionPopup data={PROGRESSION_DATA} onClose={() => setShowProgression(false)} />}
      {showShareModal && (
        <div className={isMobile ? mobileContainerClass : desktopContainerClass}>
          <div className={isMobile ? mobileContentClass : desktopContentClass}>
            <div className="p-6 flex justify-between items-center sticky top-0 rounded-t-3xl z-10">
              <h2 className="text-xl text-white font-[Anton] tracking-wide">Share Squad Invite</h2>
              <button
                onClick={() => setShowShareModal(false)}
                className="text-white/60 hover:text-white transition-colors"
              >
                <X size={24} />
              </button>
            </div>

            <div className="p-6">
              <div className="bg-white/5 rounded-lg p-4 mb-6 flex items-center justify-between">
                <div className="flex items-center gap-2 flex-1 mr-4">
                  <ExternalLink size={20} className="text-white/60" />
                  <input
                    type="text"
                    value={inviteLink}
                    readOnly
                    className="bg-transparent flex-1 focus:outline-none"
                  />
                </div>
                <button
                  onClick={handleCopyLink}
                  className="bg-white/10 hover:bg-white/20 transition-colors p-2 rounded-lg relative group"
                >
                  {copied ? (
                    <CheckCircle size={20} className="text-green-400" />
                  ) : (
                    <Copy size={20} className="text-white/60 group-hover:text-white" />
                  )}
                </button>
              </div>

              <div className="space-y-4">
                {SHARE_OPTIONS.map(option => {
                  // const IconComponent = option.icon;
                  return (
                    <button
                      key={option.id}
                      onClick={() => handleShare(option.id)}
                      className="w-full bg-white/5 hover:bg-white/10 transition-colors rounded-lg p-4 flex items-center gap-4"
                    >
                      <div className={`p-2 rounded-lg ${option.bgColor}`}>
                        <img src={option?.icon} alt="icon" className="h-6 w-6 object-contain" />
                      </div>
                      <span className="font-medium font-[Anton] tracking-wide">Share via {option.name}</span>
                    </button>
                  );
                })}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Create Squad Modal */}
      {showCreateModal && (
        <div className={isMobile ? mobileContainerClass : desktopContainerClass}>
          <div className={isMobile ? mobileContentClass : desktopContentClass}>
            <div className="p-6 flex justify-between items-center sticky top-0 rounded-t-3xl z-10">
              <h2 className="text-xl text-white font-[Anton] tracking-wide">Create New Squad</h2>
              <button
                onClick={() => setShowCreateModal(false)}
                className="text-white/60 hover:text-white transition-colors"
              >
                <X size={24} />
              </button>
            </div>

            <div className="p-6">
              <Formik
                initialValues={{ name: '', joiningType: '' }}
                validationSchema={CREATE_SQUAD_SCHEMA}
                onSubmit={handleCreateSquad}
              >
                {({ isSubmitting }) => (
                  <Form>
                    <div className="mb-6">
                      <label htmlFor="name" className="block text-sm font-medium mb-2">
                        Squad Name
                      </label>
                      <Field
                        id="name"
                        name="name"
                        placeholder="Enter squad name"
                        className="w-full px-4 py-2 rounded-lg bg-white/10 border border-white/20 text-white placeholder-white/40 focus:outline-none focus:border-yellow-400"
                      />
                      <ErrorMessage name="name" component="div" className="text-red-500 text-sm mt-1" />
                    </div>

                    <div className="mb-6">
                      <label htmlFor="joiningType" className="block text-sm font-medium mb-2">
                        Joining Type
                      </label>
                      <Field
                        as="select"
                        id="joiningType"
                        name="joiningType"
                        className="w-full px-4 py-2 rounded-lg bg-white/10 border border-white/20 text-white focus:outline-none focus:border-yellow-400"
                      >
                        <option value="">Select type</option>
                        <option value="Open">Open</option>
                        <option value="Request to Join">Request to Join</option>
                        <option value="Invite-only">Invite-only</option>
                      </Field>
                      <ErrorMessage name="joiningType" component="div" className="text-red-500 text-sm mt-1" />
                    </div>

                    <div className="flex gap-4">
                      <button
                        type="button"
                        onClick={() => setShowCreateModal(false)}
                        className="flex-1 py-2 rounded-lg bg-white/10 hover:bg-white/20 transition-colors uppercase font-[Anton] tracking-wide"
                        disabled={isSubmitting}
                      >
                        Cancel
                      </button>
                      <button
                        type="submit"
                        className="flex-1 py-2 rounded-lg bg-white text-black transition-colors uppercase font-[Anton] tracking-wide"
                        disabled={isSubmitting}
                      >
                        Create Squad
                      </button>
                    </div>
                  </Form>
                )}
              </Formik>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SquadsPage;