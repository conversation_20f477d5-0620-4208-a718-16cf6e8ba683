import React, { useEffect, useRef, useState } from 'react';
import mapImageSrc from '../assets/images/map.avif';
import { IMAGES } from '../constant/image';
import { X } from 'lucide-react';

type Marker = {
  x: number;
  y: number;
  avatar: string;
  title: string;
  price: string;
  bg_image: string;
  bg_color: string;
  share_title: string;
  share_description: string;
};

interface HoverModalProps {
  marker: Marker;
  onClose: () => void;
  setRewardVisible: (visible: boolean) => void;
  setImage: (image: string) => void;
  setBackGroundColor: (backGroundColor: string) => void;
  setBottomData: (bottomData: {}) => void
}

const HoverModal: React.FC<HoverModalProps> = ({ marker, onClose, setRewardVisible, setImage, setBackGroundColor, setBottomData }) => (
  <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
    <div
      className="relative w-full max-w-[220px] h-[320px] rounded-[24px] overflow-hidden shadow-2xl flex flex-col justify-between"
      style={{
        backgroundImage: `url(${marker.bg_image})`,
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        height: 270
      }}
    >
      {/* Overlay */}
      <div
        className="absolute top-0 left-0 w-full h-full"
        style={{
          backgroundImage: `linear-gradient(360deg, ${marker?.bg_color}, #000000a3)`,
          backgroundSize: 'cover',
          backgroundPosition: 'center',
        }}
      />



      {/* Top controls */}
      <div className="absolute top-3 left-3 z-10">
        <img src={IMAGES.LOCK} alt="Lock Icon" className="w-6 h-6" />
      </div>
      <button
        onClick={onClose}
        className="absolute top-3 right-3 text-white hover:text-gray-300 transition z-30"
        aria-label="Close"
      >
        <X size={24} />
      </button>

      {/* Main content */}
      <div className="relative z-10 flex flex-col justify-center items-center px-6 pt-24 pb-6 text-white ">
        <h2 className="text-[20px] font-bold mb-2 font-[Anton] tracking-wide">{marker.title}</h2>
        <p className="text-[12px] mb-6 font-sans font-normal text-center">
          Earn <span className="font-bold ">x Bucks</span> & <span className="font-bold">x Rise</span> when unlocked
        </p>
        <button
          onClick={() => {
            onClose(); // close current modal
            setTimeout(() => setRewardVisible(true), 200); // open reward after delay
            setImage(marker?.bg_image)
            setBackGroundColor(marker?.bg_color)
            setBottomData(marker)
          }}
          style={{
            backgroundColor: marker?.bg_color,
          }}
          className={`w-full  transition text-white  py-2 rounded-xl text-[14px] shadow-md font-[Anton] tracking-wide`}      >
          {marker.price} UNLOCK
        </button>

      </div>
    </div>
  </div>
);


const RewardModal: React.FC<{ bottomData: Marker, backGroundColor: string, image: string, onClose: () => void }> = ({ onClose, backGroundColor, bottomData, image }) => (
  <div className="fixed inset-0 z-50 flex items-end md:items-center justify-center bg-black/60 backdrop-blur-sm md:p-8 transition duration-300 ease-out">
    <div
      style={{
        backgroundImage: `url(${image})`,
        backgroundSize: 'cover',
        backgroundPosition: 'center',

      }}
      className="relative w-full max-w-md md:max-w-sm  h-[640px] md:h-[500px] rounded-t-2xl md:rounded-2xl overflow-hidden text-white shadow-2xl"
    >
      {/* Overlay */}
      {/* <div className="absolute inset-0  bg-[linear-gradient(360deg,#9509a0,#000000a3)]" /> */}
      <div
        className="absolute top-0 left-0 w-full h-full"
        style={{
          backgroundImage: `linear-gradient(360deg, ${backGroundColor}, #000000a3)`,
          backgroundSize: 'cover',
          backgroundPosition: 'center',
        }}
      />

      {/* Close Button */}
      <button
        onClick={onClose}
        className="absolute top-3 right-3 z-30 text-white hover:text-gray-300 cursor-pointer"
        aria-label="Close"
      >
        <X size={24} />
      </button>

      {/* Modal Content */}
      <div className="absolute bottom-20 md:bottom-0  w-full z-20 flex flex-col items-center  text-center px-6  md:py-12">
        <div className="flex flex-col items-center w-[252px] mb-8 md:mb-0 justify-end">
          <h2 className="text-[32px] font-[Anton] mb-3  tracking-wider">
            {bottomData?.title}
          </h2>

          <p className="text-[16px] text-white/80 mb-2 tracking-wider">
            {bottomData?.share_title}
          </p>

          <p className="text-[16px]  font-medium mt-8 md:mt-0 mb-0 md:mb-6 tracking-wider">
            {bottomData?.share_description}
          </p>
        </div>

        <button

          className="w-full bg-white  transition text-black font-bold py-2  rounded-xl text-lg font-[Anton] tracking-wide shadow-lg">
          SHARE
        </button>
      </div>

    </div>
  </div>
);




const markers: Marker[] = [
  { x: 1100, y: 620, avatar: IMAGES.MAP_1, title: 'SHOT CALLER',  price: '$100.00', bg_image: IMAGES.SHOTE_CELLER_2, bg_color: '#31ad09', share_title: "You Just Levelled Up. Keep Climbing", share_description: "You’ve just earned 12,000 Bucks & 5,000 Rise Tokens " },
  { x: 1250, y: 150, avatar: IMAGES.MAP_2, title: 'CORNER HUSTLER', price: '$100.00', bg_image: IMAGES.CORNER_HUSTLE, bg_color: '#31ad09', share_title: "The Streets Are Watching. You’re Rising", share_description: "You’ve just earned 1,000 Bucks & 100 Rise Tokens " },
  { x: 460, y: 360, avatar: IMAGES.MAP_3, title: 'BORDER RUNNER',  price: '$100.00', bg_image: IMAGES.SHOT_CALLER, bg_color: '#6309b1', share_title: "Next Rank Unlocked:The Streets Respect You", share_description: "You’ve just earned 7,000 Bucks & 1,000 Rise Tokens " },
  { x: 480, y: 1100, avatar: IMAGES.MAP_4, title: 'STREET BOSS',  price: '$100.00', bg_image: IMAGES.STREET_BOSS, bg_color: '#0888ad', share_title: "Another Step Closer to Legendary Topdog", share_description: "You’ve just earned 15,000 Bucks & 7,000 Rise Tokens " },
  { x: 300, y: 1400, avatar: IMAGES.MAP_5, title: 'UNDERBOSS',  price: '$100.00', bg_image: IMAGES.UNDERBOSS, bg_color: '#b08908', share_title: "Rank Up! Your Legend Grows.", share_description: "You’ve just earned 25,000 Bucks & 15,000 Rise Tokens " },
  { x: 1080, y: 1600, avatar: IMAGES.MAP_6, title: 'OG',  price: '$100.00', bg_image: IMAGES.OG, bg_color: '#33b009', share_title: "Power Moves Only. You’re Rising", share_description: "You’ve just earned 30,000 Bucks & 20,000 Rise Tokens " },
  { x: 830, y: 970, avatar: IMAGES.MAP_7, title: 'CONNECTOR', price: '$100.00', bg_image: IMAGES.CONNECTOR, bg_color: '#b08908', share_title: "Respect Earned. Power Gained.", share_description: "You've just earned 10,000Buck's & 2,000 Rise Tokens " },
  { x: 700, y: 1730, avatar: IMAGES.MAP_8, title: 'KINGPIN',  price: '$100.00', bg_image: IMAGES.KINGPIN, bg_color: '#088baf', share_title: "Standing On The Edge Of Legends", share_description: "You’ve just earned 40,000 Bucks & 50,000 Rise Tokens " },
  { x: 840, y: 150, avatar: IMAGES.MAP_9, title: 'STREET SCOUT', price: '$100.00', bg_image: IMAGES.STREET_SCOUT, bg_color: '#0888ad', share_title: "Level Up: Your Hustle Just Got Stronger", share_description: "You’ve just earned 5,000 Bucks & 500 Rise Tokens " },
  { x: 1150, y: 2300, avatar: IMAGES.MAP_10, title: 'LEGENDARY TOPDOG', price: '$100.00', bg_image: IMAGES.SHOT_CALLER_3, bg_color: '#6309b1', share_title: "The Kingdom Is Yours. Rule With Pride, Glory & Respect", share_description: "You’ve just earned 100,000 Rise Tokens" },
  { x: 1480, y: 1300, avatar: IMAGES.MAP_11, title: 'CAPO',  price: '$100.00', bg_image: IMAGES.CAPO, bg_color: '#6309b1', share_title: "Hustle Upgraded. New Rewards Await", share_description: "You’ve just earned 20,000 Bucks & 10,000 Rise Tokens " },
  // ... other markers
];

interface GameMapHTMLProps {
  currentLevel: number;
}

const GameMapHTML: React.FC<GameMapHTMLProps> = ({ currentLevel }) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [size, setSize] = useState({ width: 0, height: 0 });
  const [openIndex, setOpenIndex] = useState<number | null>(null);
  const [rewardVisible, setRewardVisible] = useState(false);
  const [image, setImage] = useState<string | null>(null)
  const [backGroundColor, setBackGroundColor] = useState<string | null>(null)
  const [bottomData, setBottomData] = useState<Record<string, any>>({})
  useEffect(() => {
    const handleResize = () => {
      if (containerRef.current) {
        setSize({
          width: containerRef.current.clientWidth,
          height: containerRef.current.clientHeight,
        });
      }
    };
    handleResize();
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const naturalWidth = 1920; // actual map image width
  const naturalHeight = 1080; // actual map image height
  const scale = Math.min(size.width / naturalWidth, size.height / naturalHeight);
  const avatarSize = Math.max(30, Math.min(150, 100 * scale));

  return (
    <div ref={containerRef} className="relative w-full h-[80vh] bg-black">
      <img
        src={mapImageSrc}
        alt="Game Map"
        className="w-full h-full object-cover"
      />

      {markers.map((marker, idx) => {
        const left = marker.x * scale;
        const top = marker.y * scale;
        const isLocked = idx + 1 > currentLevel;
        return (
          <button
            key={idx}
            onClick={() => setOpenIndex(idx)}
            style={{ left, top }}
            className="absolute transform -translate-x-1/2 -translate-y-1/2 rounded-full bg-white/30 flex items-center justify-center"
            disabled={false} // Optionally disable if you want to prevent clicking locked markers
          >
            <div className="absolute w-full h-full rounded-full bg-white/30" />
            <div className="relative flex items-center justify-center">
              <img
                src={marker.avatar}
                alt={marker.title}
                className={`w-[${avatarSize}px] h-[${avatarSize}px] ${idx !== 0 && 'grayscale'} rounded-full object-cover border-2 border-white/10 box-border`}
              />
              {isLocked && (
                <img
                  src={IMAGES.LOCK}
                  alt="Locked"
                  className="absolute top-1 right-1 w-6 h-6 z-10 drop-shadow-md"
                  style={{ pointerEvents: 'none' }}
                />
              )}
            </div>
          </button>
        );
      })}

      {openIndex !== null && (
        <HoverModal
          marker={markers[openIndex]}
          onClose={() => setOpenIndex(null)}
          setRewardVisible={setRewardVisible}
          setImage={setImage}
          setBackGroundColor={setBackGroundColor}
          setBottomData={setBottomData}
        />
      )}
      {rewardVisible && (
        <RewardModal onClose={() => setRewardVisible(false)} image={image ?? ''} backGroundColor={backGroundColor ?? ''} bottomData={bottomData as Marker} />
      )}

    </div>
  );
};

export default GameMapHTML;
