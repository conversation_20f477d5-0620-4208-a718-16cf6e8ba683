import React, { useState, useEffect, useRef } from 'react';
import { X, CheckCircle } from 'lucide-react';

interface VideoAdProps {
  onClose: () => void;
  onComplete?: () => void;
}

const VideoAd: React.FC<VideoAdProps> = ({ onClose, onComplete }) => {
  const [progress, setProgress] = useState(0);
  const [isVideoLoaded, setIsVideoLoaded] = useState(false);
  const [showReward, setShowReward] = useState(false);
  const videoRef = useRef<HTMLVideoElement>(null);
  const rewardTimeoutRef = useRef<NodeJS.Timeout>();

  useEffect(() => {
    if (progress >= 100) {
      setShowReward(true);
      onComplete?.();
      rewardTimeoutRef.current = setTimeout(() => {
        onClose();
      }, 2000);
    }
    return () => {
      if (rewardTimeoutRef.current) {
        clearTimeout(rewardTimeoutRef.current);
      }
    };
  }, [progress, onComplete, onClose]);

  const handleTimeUpdate = () => {
    if (videoRef.current) {
      const percentage = (videoRef.current.currentTime / videoRef.current.duration) * 100;
      setProgress(Math.min(percentage, 100));
    }
  };

  const handleVideoLoad = () => {
    setIsVideoLoaded(true);
    if (videoRef.current) {
      videoRef.current.play().catch(error => {
        console.error('Video playback failed:', error);
      });
    }
  };

  return (
    <div className="fixed inset-0 bg-black/90 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-gradient-to-b from-purple-900 to-indigo-900 rounded-2xl p-6 w-full max-w-lg">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl font-bold text-white">Watch Ad to Earn FPP</h2>
          <button 
            onClick={onClose}
            className="text-white/60 hover:text-white transition-colors"
          >
            <X size={24} />
          </button>
        </div>

        <div className="relative">
          <div className="aspect-video w-full rounded-xl overflow-hidden bg-black">
            {!isVideoLoaded && (
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="animate-pulse text-white/60">Loading video...</div>
              </div>
            )}
            <video
              ref={videoRef}
              autoPlay
              muted
              playsInline
              onLoadedData={handleVideoLoad}
              onTimeUpdate={handleTimeUpdate}
              className="w-full h-full object-cover"
              src="https://storage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4"
            />
          </div>

          {/* Progress Bar */}
          <div className="absolute bottom-0 left-0 right-0 h-1 bg-white/20">
            <div 
              className="h-full bg-yellow-400 transition-all duration-300"
              style={{ width: `${progress}%` }}
            />
          </div>

          {/* Reward Notification */}
          {showReward && (
            <div className="absolute inset-0 flex items-center justify-center bg-black/80 backdrop-blur-sm">
              <div className="text-center animate-fade-in">
                <CheckCircle size={48} className="text-green-400 mx-auto mb-3" />
                <p className="text-xl font-bold text-white">50 FPP Earned! 🎉</p>
              </div>
            </div>
          )}
        </div>

        <div className="mt-6 flex justify-between items-center">
          <p className="text-white/60 text-sm">
            Watch progress: {Math.round(progress)}%
          </p>
          <p className="text-sm font-medium text-yellow-400">
            Reward: 50 FPP
          </p>
        </div>
      </div>
    </div>
  );
};

export default VideoAd;