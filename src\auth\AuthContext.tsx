import React, { createContext, useContext, useState } from 'react';

/**
 * AuthContextType defines the shape of the authentication context.
 * @property {boolean} isAuthenticated - Whether the user is logged in
 * @property {string | null} accessToken - JWT or bearer token for API requests
 * @property {(token: string) => void} login - Function to log in and store token
 * @property {() => void} logout - Function to log out and clear token
 */
interface AuthContextType {
  isAuthenticated: boolean;
  accessToken: string | null;
  login: (token: string) => void;
  logout: () => void;
}

/**
 * Create the AuthContext with default values.
 * Consumers should use useAuth() hook to access context.
 */
const AuthContext = createContext<AuthContextType>({
  isAuthenticated: false,
  accessToken: null,
  login: () => {},
  logout: () => {},
});

/**
 * Custom hook to access authentication context.
 * @returns {AuthContextType}
 */
export const useAuth = () => useContext(AuthContext);

/**
 * AuthProvider component
 * Wrap your application to provide authentication state.
 * 
 * @param {{ children: React.ReactNode }} props - Child components
 * @returns {JSX.Element}
 */
export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  /**
   * accessToken state is initialized from localStorage if available.
   */
  const [accessToken, setAccessToken] = useState<string | null>(() => {
    return localStorage.getItem('access_token');
  });

  /**
   * login: save token to localStorage and update state
   * @param {string} token - The access token to store
   */
  const login = (token: string) => {
    localStorage.setItem('access_token', token);
    setAccessToken(token);
  };

  /**
   * logout: remove token from localStorage and update state
   */
  const logout = () => {
    localStorage.removeItem('access_token');
    setAccessToken(null);
  };

  /**
   * Context value to pass down
   */
  const value: AuthContextType = {
    isAuthenticated: !!accessToken,
    accessToken,
    login,
    logout,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};