import React, { useEffect, useState } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import Layout from './components/Layout';
import Home from './pages/Home';
import Shop from './pages/Shop';
import Wallet from './pages/Wallet';
import Rewards from './pages/Rewards';
import Leaderboard from './pages/Leaderboard';
import Squads from './pages/Squads';
import Auth from './pages/Auth';
import StoryIntro from './components/StoryIntro';
import CrashGame from './pages/CrashGame';
import CrashGame2 from './pages/CrashGame2';
import CryptoKing from './pages/CryptoKing';
import HigherLowerGame from './pages/HigherLowerGame';
import RollDiceGame from './pages/RollDiceGame';
import Map from './pages/Map';
import ScratchCard from './components/ScratchCard';
import BoostModal from './components/BoostModal';
import WheelOfFortune from './components/WheelOfFortune';
import ProtectedRoute from './components/ProtectedRoute';
import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { useAuth } from './auth/AuthContext';
import { GET_BALANCE_API } from './api/auth';
import { useSocketContext } from './context/socketProvider';
import PlinkoGame from './pages/PlinkoGame';
import TermsAndConditionsPage from './components/TermsAndConditions';
import PrivacyPolicyPage from './components/PrivacyPolicy';
import { useGameDataLoading } from './hooks/useGameDataLoading';
function App() {
  const [balance, setBalance] = useState({
    coins: 0,
    fpp: 0,
    level: 0,
    xp: 0,
    real_money: '0'
  });
  const {
    connectSocket,
    isConnected,
  } = useSocketContext();
  useGameDataLoading();

  const { isAuthenticated, login, accessToken } = useAuth();
  const [showStoryIntro, setShowStoryIntro] = useState(false);
  const [showScratchCard, setShowScratchCard] = useState(false);
  const [showBoostModal, setShowBoostModal] = useState(false);
  const [showWheel, setShowWheel] = useState(false);
  const [data, setData] = useState<any[]>([]);
  // const { data } = useFetch(GET_BALANCE_API, {
  //   headers: {
  //     Authorization: `Bearer ${accessToken}`
  //   }
  // });


  useEffect(() => {
    if (!isAuthenticated || !accessToken) return;

    fetch(GET_BALANCE_API, {
      headers: { Authorization: `Bearer ${accessToken}` }
    })
      .then(res => {
        if (!res.ok) throw new Error(`HTTP ${res.status}`);
        return res.json();
      })
      .then(data => setData(data))
      .catch(err => console.error('Balance fetch failed:', err));
  }, [isAuthenticated, accessToken]);

  useEffect(() => {
    const hasSeenIntro = localStorage.getItem('hasSeenStoryIntro');

    if (!hasSeenIntro) {
      const timer = setTimeout(() => setShowStoryIntro(true), 500);
      return () => clearTimeout(timer);
    }
  }, []);
  useEffect(() => {
    if (!isConnected) {

      connectSocket()
    }
  }, [isConnected])

  useEffect(() => {
    if (data && data.length > 0) {
      const filterPrice = data.filter((item: { price: string }) => item.currency === 'USD');
      setBalance(prev => ({
        ...prev,
        // real_money: filterPrice[0].real_money, // Add real_money to the balance state
      }));
    }

  }, [data]);

  const handleLogin = async (token: string) => {
    login(token);

    // setShowStoryIntro(true);
  };

  // Listen for custom events
  React.useEffect(() => {
    const handleShowScratchCard = () => {
      setShowScratchCard(true);
    };

    const handleShowBoostModal = () => {
      setShowBoostModal(true);
    };

    const handleShowWheel = () => {
      setShowWheel(true);
    };

    window.addEventListener('show-scratch-card', handleShowScratchCard);
    window.addEventListener('show-boost-modal', handleShowBoostModal);
    window.addEventListener('show-wheel', handleShowWheel);

    return () => {
      window.removeEventListener('show-scratch-card', handleShowScratchCard);
      window.removeEventListener('show-boost-modal', handleShowBoostModal);
      window.removeEventListener('show-wheel', handleShowWheel);
    };
  }, []);


  const handleWin = (reward: { type: string; amount: number }) => {
    if (reward.type === 'coins') {
      setBalance(prev => ({
        ...prev,
        coins: prev.coins + reward.amount
      }));
    } else if (reward.type === 'fpp') {
      setBalance(prev => ({
        ...prev,
        fpp: prev.fpp + reward.amount
      }));
    }
  };

  const handleShare = (platform: string) => {
    // Implement share functionality
    console.log(`Sharing to ${platform}`);
    setShowBoostModal(false);
    // Add Bucks reward for sharing
    setBalance(prev => ({
      ...prev,
      xp: Math.min(100, prev.xp + 10)
    }));
  };

  return (
    // <SocketProvider>
    <Router>

      <Routes>
        <Route path="/auth" element={
          isAuthenticated ? (
            <Navigate to="/" replace />
          ) : (
            <Auth onLogin={handleLogin} />
          )
        } />
        <Route
          path="/"
          element={
            <ProtectedRoute isAuthenticated={isAuthenticated}>
              <>
                <Layout balance={balance} />
                {showStoryIntro && (
                  <div className="fixed inset-0 bg-black z-50">
                    <StoryIntro onClose={() => setShowStoryIntro(false)} />
                  </div>
                )}
                {showScratchCard && (
                  <ScratchCard
                    onClose={() => setShowScratchCard(false)}
                    onWin={handleWin}
                    // cost={100}
                    balance={balance.coins}
                    onPurchase={(cost) => {
                      setBalance(prev => ({
                        ...prev,
                        coins: prev.coins - cost
                      }));
                    }}
                  />
                )}
                {showBoostModal && (
                  <BoostModal
                    onClose={() => setShowBoostModal(false)}
                    onWatchAd={() => {
                      setShowBoostModal(false);
                      // Handle video ad
                    }}
                    onShare={handleShare}
                  />
                )}
                {showWheel && (
                  <WheelOfFortune
                    onClose={() => setShowWheel(false)}
                    onWin={handleWin}
                  />
                )}


                <ToastContainer position="top-right" />


              </>
            </ProtectedRoute>
          }
        >
          <Route path="/terms-and-conditions" element={<TermsAndConditionsPage />} />
            <Route path="/privacy-policy" element={<PrivacyPolicyPage />} />
          <Route index element={<Home balance={balance} onBalanceChange={setBalance} />} />
          <Route path="shop" element={<Shop balance={balance} onBalanceChange={setBalance} />} />
          <Route path="wallet" element={<Wallet balance={balance} />} />
          <Route path="squads" element={<Squads />} />
          <Route path="rewards" element={<Rewards balance={balance} onBalanceChange={setBalance} />} />
          <Route path="leaderboard" element={<Leaderboard />} />
          <Route path="map" element={<Map />} />
          <Route path="street-king" element={<CrashGame />} />
          <Route path="street-king-2" element={<CrashGame2 />} />
          <Route path="crypto-king" element={<CryptoKing />} />
          <Route path="quick-hustle" element={<HigherLowerGame />} />
          <Route path="roll-dice" element={<RollDiceGame />} />
          <Route path="plinko" element={<PlinkoGame balance={balance} />} />

        </Route>
      </Routes>
    </Router>
    // </SocketProvider>
  );
}

export default App;