import axios from 'axios';
import {
  PLACE_BET_API,
  HISTORY_GET_API,
  QUICK_HUSTLES_SELECTS_API
} from './auth';


export interface HistoryData {
    page: number;
    per_page: number;
    version: string;
  }

export interface SelectQueryParams {
    id: string,
    user_guess: string,
    user_id: string
  }
// 🔹 Place a bet
export const placeBetForQuickHustles = async (
  data: {bet_amount:number},
  token: string
): Promise<any> => {
  const response = await axios.post(PLACE_BET_API, data, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
  return response.data;
};

// 🔹 Quick Hustles Select Api
export const SelectForQuickHustels = async (
  data: {
    id: string;
    user_guess: 'HIGHER' | 'LOWER';
    user_id: string;
  },
    token: string
  ): Promise<any> => {
    const response = await axios.post(QUICK_HUSTLES_SELECTS_API, data, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
    return response.data;
  };

export const getQuickHustlesHistory = async (data: HistoryData, token: string) => {
    const response = await axios.get(HISTORY_GET_API, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
      params: {
        page: data.page,
        perPage: data.per_page,
      },
    });
  
    return response.data;
  };